import { app, BrowserWindow, Menu, shell } from 'electron';
import path from 'path';
import { fileURLToPath } from 'url';
import { ipcMain } from 'electron';
import fs from 'fs';
// Audio recording related imports
import { initAudioSystem, checkAudioPermissions, requestAudioPermissions, enableLoopbackAudio, disableLoopbackAudio, startRecording as startLoopbackRecording, stopRecording as stopLoopbackRecording, } from './audio-recorder';
import { ipcEvents } from './audio-config';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// Keep a global reference of the window object
let mainWindow = null;
const isDevelopment = process.env.NODE_ENV === 'development';
function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        titleBarStyle: 'hiddenInset', // macOS style title bar
        trafficLightPosition: { x: 16, y: 12 }, // Position of window controls
        show: false, // Don't show until ready-to-show
    });
    // Load the app
    if (isDevelopment) {
        mainWindow.loadURL('http://localhost:5173');
        // Open DevTools in development
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    }
    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
        if (isDevelopment) {
            mainWindow?.focus();
        }
    });
    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}
// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
    // Initialize the audio system with default options.
    initAudioSystem();
    createWindow();
    // ---------------------- IPC HANDLERS ----------------------
    // Permissions
    ipcMain.handle(ipcEvents.checkPermissions, async () => {
        return await checkAudioPermissions();
    });
    ipcMain.handle(ipcEvents.requestPermissions, async () => {
        return await requestAudioPermissions();
    });
    // Loopback control
    ipcMain.handle(ipcEvents.enableLoopbackAudio, async () => {
        await enableLoopbackAudio();
        return true;
    });
    ipcMain.handle(ipcEvents.disableLoopbackAudio, async () => {
        disableLoopbackAudio();
        return true;
    });
    // Recording control
    ipcMain.handle(ipcEvents.startRecording, async () => {
        return await startLoopbackRecording();
    });
    ipcMain.handle(ipcEvents.stopRecording, async () => {
        return await stopLoopbackRecording();
    });
    // Save audio file buffer coming from renderer
    ipcMain.handle(ipcEvents.saveAudioFile, async (_event, { filePath, data }) => {
        try {
            await fs.promises.writeFile(filePath, Buffer.from(data));
            return { success: true };
        }
        catch (error) {
            console.error('Failed to save audio file:', error);
            return { success: false, message: error instanceof Error ? error.message : 'Unknown error' };
        }
    });
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
    // Set up the menu
    if (process.platform === 'darwin') {
        const template = [
            {
                label: app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideothers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            },
            {
                label: 'Edit',
                submenu: [
                    { role: 'undo' },
                    { role: 'redo' },
                    { type: 'separator' },
                    { role: 'cut' },
                    { role: 'copy' },
                    { role: 'paste' }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' }
                ]
            }
        ];
        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }
});
// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});
app.on('activate', () => {
    if (mainWindow === null) {
        createWindow();
    }
});
// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
    contents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
});
